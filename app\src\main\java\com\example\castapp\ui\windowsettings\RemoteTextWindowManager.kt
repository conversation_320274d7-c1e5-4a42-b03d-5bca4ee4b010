package com.example.castapp.ui.windowsettings

import android.content.Context
import android.widget.FrameLayout
import com.example.castapp.model.RemoteReceiverConnection
import com.example.castapp.ui.view.TextEditPanel
import com.example.castapp.ui.view.TextWindowView
import com.example.castapp.utils.AppLog
import com.example.castapp.utils.TextFormatManager
import com.example.castapp.websocket.ControlMessage
import java.lang.ref.WeakReference

/**
 * 遥控端文字窗口管理器
 * 专门为遥控端文字窗口编辑功能设计，复用现有的TextEditPanel和TextWindowView
 *
 * 🎯 优化说明：
 * - 直接使用WindowVisualizationContainerView中已有的TextWindowView
 * - 移除了历史遗留的TextView查找和替换逻辑
 * - 通过属性控制实现显示/编辑模式切换，无需控件替换
 */
class RemoteTextWindowManager(
    private val context: Context,
    private val textId: String,
    private val initialTextContent: String,
    private val remoteReceiverConnection: RemoteReceiverConnection,
    private val windowVisualizationView: com.example.castapp.ui.view.WindowContainerVisualizationView? = null
) {

    // 遥控端文字窗口的可视化容器视图
    private var targetWindowContainerView: com.example.castapp.ui.view.WindowVisualizationContainerView? = null

    // 遥控端创建的真正的TextWindowView（用于编辑）
    private var remoteTextWindowView: com.example.castapp.ui.view.TextWindowView? = null

    // 编辑面板
    private var textEditPanel: TextEditPanel? = null

    // 格式管理器
    private val textFormatManager = TextFormatManager(context)

    // 🎯 架构重构：移除本地状态变量，统一配置管理器为唯一数据源
    // 所有状态数据都从统一配置管理器获取，不在此处存储

    // 统一配置管理器实例
    private val configManager = com.example.castapp.model.RemoteWindowConfigManager.getInstance()

    // 主容器的弱引用
    private var mainContainerRef: WeakReference<FrameLayout>? = null

    // 🎯 防止重复调用hideEditPanel的标志
    private var isHidingEditPanel = false

    // 🎯 同步回调函数（由RemoteWindowManagerDialog设置）
    private var syncCallback: (() -> Boolean)? = null

    /**
     * 🎯 架构重构：从统一配置管理器获取当前窗口配置
     */
    private fun getCurrentConfig(): com.example.castapp.model.RemoteWindowConfig? {
        return try {
            configManager.getWindowConfig(remoteReceiverConnection.id, textId)
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】获取统一配置失败", e)
            null
        }
    }

    /**
     * 🎯 架构重构：从统一配置同步所有数据到UI组件
     */
    private fun syncAllDataFromConfig() {
        try {
            val config = getCurrentConfig()
            if (config != null) {
                // 同步到TextWindowView
                remoteTextWindowView?.let { textView ->
                    textView.setTextContent(config.textContent)
                    textView.setBoldEnabled(config.isBold)
                    textView.setItalicEnabled(config.isItalic)
                    textView.setFontSize(config.fontSize)
                    textView.setWindowBackgroundColor(config.isWindowTextColorEnabled, config.windowTextBackgroundColor)

                    AppLog.d("【遥控端文字窗口管理器】🎯 已从统一配置同步数据到TextWindowView")
                }

                // 同步到编辑面板
                textEditPanel?.let { panel ->
                    panel.initializeWindowColorState(config.isWindowTextColorEnabled, config.windowTextBackgroundColor)
                    AppLog.d("【遥控端文字窗口管理器】🎯 已从统一配置同步数据到编辑面板")
                }

                AppLog.d("【遥控端文字窗口管理器】🎯 统一配置数据同步完成")
            } else {
                AppLog.w("【遥控端文字窗口管理器】统一配置为空，无法同步数据")
            }
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】从统一配置同步数据失败", e)
        }
    }

    /**
     * 🎯 架构重构：验证数据一致性（调试用）
     */
    private fun validateDataConsistency() {
        try {
            val config = getCurrentConfig()
            if (config != null) {
                val textViewState = remoteTextWindowView?.getWindowBackgroundColorState()

                AppLog.d("【数据一致性验证】统一配置: 启用=${config.isWindowTextColorEnabled}, 颜色=${String.format("#%08X", config.windowTextBackgroundColor)}")
                AppLog.d("【数据一致性验证】TextWindowView: 启用=${textViewState?.first}, 颜色=${textViewState?.second?.let { String.format("#%08X", it) }}")

                if (textViewState != null) {
                    val isConsistent = config.isWindowTextColorEnabled == textViewState.first &&
                                     config.windowTextBackgroundColor == textViewState.second
                    AppLog.d("【数据一致性验证】数据一致性: $isConsistent")
                }
            }
        } catch (e: Exception) {
            AppLog.e("【数据一致性验证】验证失败", e)
        }
    }

    /**
     * 🎯 设置同步回调函数
     */
    fun setSyncCallback(callback: () -> Boolean) {
        this.syncCallback = callback
    }

    /**
     * 显示编辑面板
     */
    fun showEditPanel() {
        try {
            AppLog.d("【遥控端文字窗口管理器】开始显示编辑面板: $textId")

            // 找到遥控端对应的文字窗口可视化视图
            if (!findRemoteTextWindowView()) {
                AppLog.w("【遥控端文字窗口管理器】无法找到遥控端文字窗口视图")
                return
            }

            // 获取主容器（用于显示编辑面板）
            val mainContainer = getMainContainer()
            if (mainContainer == null) {
                AppLog.w("【遥控端文字窗口管理器】无法获取主容器，无法显示编辑面板")
                return
            }

            // 如果编辑面板已存在且正在显示，则不重复创建
            if (textEditPanel?.isShowing() == true) {
                AppLog.d("【遥控端文字窗口管理器】编辑面板已在显示中")
                return
            }

            // 让TextView进入可编辑状态
            enableTextViewEditing()

            // 🎯 架构重构：从统一配置同步所有数据到UI组件
            syncAllDataFromConfig()

            // 创建编辑面板
            textEditPanel = TextEditPanel(context, mainContainer)

            // 设置监听器
            setupEditPanelListeners()

            // 🎯 架构重构：从统一配置获取所有格式状态
            val config = getCurrentConfig()
            if (config != null) {
                // 显示编辑面板，使用统一配置中的数据
                textEditPanel?.show(
                    currentText = config.textContent,
                    bold = config.isBold,
                    italic = config.isItalic,
                    fontSize = config.fontSize,
                    fontFamily = config.fontFamily,
                    letterSpacing = 0.0f, // 暂时使用默认值
                    lineSpacing = config.lineSpacing,
                    textAlignment = config.textAlignment
                )

                AppLog.d("【遥控端文字窗口管理器】🎯 编辑面板显示完成，使用统一配置数据: 加粗=${config.isBold}, 倾斜=${config.isItalic}, 字号=${config.fontSize}sp, 行间距=${config.lineSpacing}dp, 对齐=${config.textAlignment}")
            } else {
                AppLog.w("【遥控端文字窗口管理器】统一配置为空，使用默认值显示编辑面板")
                textEditPanel?.show(
                    currentText = "默认文字",
                    bold = false,
                    italic = false,
                    fontSize = 13,
                    fontFamily = null,
                    letterSpacing = 0.0f,
                    lineSpacing = 0.0f,
                    textAlignment = android.view.Gravity.CENTER
                )
            }

            AppLog.d("【遥控端文字窗口管理器】编辑面板显示完成，使用完整格式状态: 加粗=$isBoldEnabled, 倾斜=$isItalicEnabled, 字号=${currentFontSize}sp, 行间距=${currentLineSpacing}dp, 对齐=$currentAlignment")

        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】显示编辑面板失败", e)
        }
    }

    /**
     * 隐藏编辑面板（带同步控制）
     */
    fun hideEditPanelWithSync(shouldSync: Boolean) {
        try {
            // 🎯 防止重复调用
            if (isHidingEditPanel) {
                AppLog.d("【遥控端文字窗口管理器】编辑面板正在隐藏中，跳过重复调用: $textId")
                return
            }
            isHidingEditPanel = true

            AppLog.d("【遥控端文字窗口管理器】隐藏编辑面板: $textId, 是否同步: $shouldSync")

            // 让TextView退出编辑状态
            disableTextViewEditing()

            // 隐藏编辑面板
            textEditPanel?.hide()

            // 根据参数决定是否同步格式数据
            if (shouldSync) {
                // 强制同步，不检查同步开关状态
                forceSyncFormatData()
                AppLog.d("【遥控端文字窗口管理器】编辑内容已强制同步到接收端: $textId")
            } else {
                // 🎯 关键修复：即使不同步到接收端，也要更新统一配置管理器，确保遥控端内部数据一致性
                val formatData = collectCurrentFormatData()
                val actualTextContent = formatData["textContent"] as? String ?: currentTextContent
                updateUnifiedConfigTextContent(actualTextContent, formatData)
                AppLog.d("【遥控端文字窗口管理器】编辑内容已保存到统一配置管理器（不同步到接收端）: $textId")
            }

        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】隐藏编辑面板失败", e)
        } finally {
            // 🎯 重置标志
            isHidingEditPanel = false
        }
    }

    /**
     * 隐藏编辑面板（默认行为，检查同步开关状态）
     */
    fun hideEditPanel() {
        try {
            // 🎯 防止重复调用
            if (isHidingEditPanel) {
                AppLog.d("【遥控端文字窗口管理器】编辑面板正在隐藏中，跳过重复调用: $textId")
                return
            }
            isHidingEditPanel = true

            AppLog.d("【遥控端文字窗口管理器】隐藏编辑面板: $textId")

            // 让TextView退出编辑状态
            disableTextViewEditing()

            // 隐藏编辑面板
            textEditPanel?.hide()

            // 检查是否需要同步格式数据（根据同步开关状态）
            checkAndSyncFormatData()

        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】隐藏编辑面板失败", e)
        } finally {
            // 🎯 重置标志
            isHidingEditPanel = false
        }
    }

    /**
     * 让TextWindowView退出可编辑状态
     */
    private fun disableTextViewEditing() {
        try {
            remoteTextWindowView?.let { textWindowView ->
                AppLog.d("【遥控端文字窗口管理器】让TextWindowView退出编辑模式")

                // 使用TextWindowView的标准退出编辑模式
                textWindowView.exitEditMode()

                AppLog.d("【遥控端文字窗口管理器】TextWindowView已退出编辑模式")
            }

        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】禁用TextWindowView编辑失败", e)
        }
    }

    /**
     * 查找遥控端文字窗口视图
     */
    private fun findRemoteTextWindowView(): Boolean {
        try {
            if (windowVisualizationView == null) {
                AppLog.w("【遥控端文字窗口管理器】窗口可视化容器为空")
                return false
            }

            // 在WindowContainerVisualizationView中查找对应的WindowVisualizationContainerView
            targetWindowContainerView = findWindowVisualizationContainerView(windowVisualizationView!!, textId)
            if (targetWindowContainerView == null) {
                AppLog.w("【遥控端文字窗口管理器】未找到对应的文字窗口可视化视图: $textId")
                return false
            }

            // 🎯 关键修复：直接使用容器中已有的TextWindowView，不需要创建新的
            remoteTextWindowView = findTextWindowViewInContainer(targetWindowContainerView!!)
            if (remoteTextWindowView == null) {
                AppLog.w("【遥控端文字窗口管理器】容器中未找到TextWindowView: $textId")
                return false
            }

            // 设置当前文本内容
            currentTextContent = remoteTextWindowView!!.text?.toString() ?: initialTextContent

            AppLog.d("【遥控端文字窗口管理器】成功找到遥控端文字窗口视图: $textId")
            return true

        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】查找遥控端文字窗口视图失败", e)
            return false
        }
    }

    /**
     * 在WindowContainerVisualizationView中查找WindowVisualizationContainerView
     */
    private fun findWindowVisualizationContainerView(containerView: com.example.castapp.ui.view.WindowContainerVisualizationView, targetConnectionId: String): com.example.castapp.ui.view.WindowVisualizationContainerView? {
        AppLog.d("【遥控端文字窗口管理器】在WindowContainerVisualizationView中查找: $targetConnectionId")

        try {
            // 通过反射获取windowContainerViews字段
            val windowContainerViewsField = containerView::class.java.getDeclaredField("windowContainerViews")
            windowContainerViewsField.isAccessible = true
            val windowContainerViews = windowContainerViewsField.get(containerView) as? Map<String, com.example.castapp.ui.view.WindowVisualizationContainerView>

            if (windowContainerViews != null) {
                AppLog.d("【遥控端文字窗口管理器】找到windowContainerViews，包含${windowContainerViews.size}个窗口")

                // 直接通过connectionId获取对应的WindowVisualizationContainerView
                val targetView = windowContainerViews[targetConnectionId]
                if (targetView != null) {
                    AppLog.d("【遥控端文字窗口管理器】找到匹配的窗口容器视图: $targetConnectionId")
                    return targetView
                } else {
                    AppLog.w("【遥控端文字窗口管理器】未找到对应的窗口容器视图: $targetConnectionId")
                    AppLog.d("【遥控端文字窗口管理器】可用的窗口ID: ${windowContainerViews.keys}")
                }
            } else {
                AppLog.w("【遥控端文字窗口管理器】无法获取windowContainerViews")
            }

        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】通过反射查找窗口容器视图失败", e)
        }

        return null
    }





    /**
     * 在容器中查找TextWindowView
     */
    private fun findTextWindowViewInContainer(containerView: android.view.ViewGroup): com.example.castapp.ui.view.TextWindowView? {
        for (i in 0 until containerView.childCount) {
            val child = containerView.getChildAt(i)
            if (child is com.example.castapp.ui.view.TextWindowView) {
                AppLog.d("【遥控端文字窗口管理器】找到TextWindowView: ${child.tag}")

                // 🎯 关键修复：为找到的TextWindowView设置必要的监听器
                setupTextWindowViewListeners(child)

                return child
            }
        }
        AppLog.w("【遥控端文字窗口管理器】容器中未找到TextWindowView")
        return null
    }

    /**
     * 为TextWindowView设置监听器
     */
    private fun setupTextWindowViewListeners(textWindowView: com.example.castapp.ui.view.TextWindowView) {
        try {
            // 设置文本变化监听器
            textWindowView.setOnTextChangeListener { newText ->
                currentTextContent = newText
                AppLog.d("【遥控端文字窗口管理器】文本内容已更新: '$newText'")
            }

            // 设置编辑状态监听器
            textWindowView.setOnEditStateChangeListener { isEditing ->
                if (isEditing) {
                    AppLog.d("【遥控端文字窗口管理器】文字窗口进入编辑模式")
                    // 🎯 关键修复：启用边框拖动调整大小功能
                    enableBorderResizing()
                } else {
                    AppLog.d("【遥控端文字窗口管理器】文字窗口退出编辑模式")
                    // 🎯 关键修复：禁用边框拖动调整大小功能
                    disableBorderResizing()
                    // 🎯 修复：避免重复调用hideEditPanel
                    if (!isHidingEditPanel) {
                        hideEditPanel()
                    }
                }
            }

            // 🎯 关键修复：设置选择变化监听器，用于更新格式面板状态
            textWindowView.setOnSelectionChangeWithAllFormatsListener { bold, italic, textColor, strokeState ->
                // 获取选中文字的完整格式状态（包含字号）
                val formatState = textWindowView.getSelectionFormatStateWithFontSize()
                val fontSize = formatState.third

                // 获取选中文字的字体
                val fontFamily = textWindowView.getSelectionFont()

                // 获取选中文字的字间距
                val letterSpacing = textWindowView.getSelectionLetterSpacing()

                // 获取选中文字的行间距（从TextView获取）
                val lineSpacing = textWindowView.getSelectionLineSpacing()

                // 获取文本对齐方式
                val textAlignment = textWindowView.gravity

                // 更新编辑面板的按钮状态
                textEditPanel?.updateButtonStatesFromSelection(
                    bold = bold,
                    italic = italic,
                    fontSize = fontSize,
                    textColor = textColor,
                    strokeState = strokeState,
                    fontFamily = fontFamily,
                    letterSpacing = letterSpacing,
                    lineSpacing = lineSpacing,
                    textAlignment = textAlignment
                )

                AppLog.d("【遥控端文字窗口管理器】选中文字格式状态已更新到编辑面板: 加粗=$bold, 倾斜=$italic, 字号=${fontSize}sp, 颜色=${textColor?.let { String.format("#%08X", it) } ?: "默认"}")
            }

            AppLog.d("【遥控端文字窗口管理器】TextWindowView监听器设置完成")

        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】设置TextWindowView监听器失败", e)
        }
    }
    /**
     * 让TextWindowView进入可编辑状态
     */
    private fun enableTextViewEditing() {
        try {
            remoteTextWindowView?.let { textWindowView ->
                AppLog.d("【遥控端文字窗口管理器】开始让TextWindowView进入编辑模式")

                // 使用TextWindowView的标准编辑模式
                textWindowView.enterEditMode()

                AppLog.d("【遥控端文字窗口管理器】TextWindowView已进入编辑模式")
            }

        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】启用TextWindowView编辑失败", e)
        }
    }

    /**
     * 设置编辑面板监听器
     */
    private fun setupEditPanelListeners() {
        textEditPanel?.setOnFormatChangeListener { bold, italic ->
            isBoldEnabled = bold
            isItalicEnabled = italic
            applyFormatChangesRealTime(bold, italic)
        }

        textEditPanel?.setOnFontSizeChangeListener { fontSize ->
            currentFontSize = fontSize
            applyFontSizeChangeRealTime(fontSize)
        }

        // 设置字体变化监听器
        textEditPanel?.setOnFontFamilyChangeListener { fontItem ->
            applyFontFamilyChangeRealTime(fontItem)
        }

        // 设置字间距变化监听器
        textEditPanel?.setOnLetterSpacingChangeListener { letterSpacing ->
            applyLetterSpacingChangeRealTime(letterSpacing)
        }

        // 设置行间距变化监听器
        textEditPanel?.setOnLineSpacingChangeListener { lineSpacing ->
            applyLineSpacingChangeRealTime(lineSpacing)
        }

        // 设置文本对齐变化监听器
        textEditPanel?.setOnTextAlignmentChangeListener { alignment ->
            applyTextAlignmentChangeRealTime(alignment)
        }

        // 🎯 关键修复：添加字色变化监听器
        textEditPanel?.setOnColorChangeListener { color ->
            applyColorChangeRealTime(color)
        }

        // 🎯 关键修复：添加描边变化监听器
        textEditPanel?.setOnStrokeChangeListener { enabled, width, color ->
            applyStrokeChangeRealTime(enabled, width, color)
        }

        // 🎯 关键修复：添加窗色变化监听器
        textEditPanel?.setOnWindowColorChangeListener { enabled, color ->
            applyWindowColorChangeRealTime(enabled, color)
        }

        // 🎯 关键修复：添加清除格式监听器
        textEditPanel?.setOnClearFormatListener {
            clearSelectionFormat()
        }

        // 🎯 关键修复：添加拖动监听器（拖动整个窗口容器位置）
        textEditPanel?.setOnTextWindowDragListener { deltaX, deltaY ->
            performWindowContainerDrag(deltaX, deltaY)
        }

        textEditPanel?.setOnTextWindowDragEndListener {
            notifyWindowContainerDragEnd()
        }

        textEditPanel?.setOnCloseListener {
            AppLog.d("【遥控端文字窗口管理器】用户关闭编辑面板")
            // 🎯 关键修复：根据同步开关状态决定是否同步，与编辑开关关闭行为保持一致
            val shouldSync = isSyncEnabled()
            hideEditPanelWithSync(shouldSync)

            // 🔧 统一编辑状态管理：用户通过关闭图标退出编辑模式时，更新WindowSettingsManager编辑状态
            try {
                val windowSettingsManager = com.example.castapp.manager.WindowSettingsManager.getInstance()
                windowSettingsManager.setEditState(textId, false)
                AppLog.d("【遥控端文字窗口管理器】已更新编辑开关状态为关闭: $textId")
            } catch (e: Exception) {
                AppLog.e("【遥控端文字窗口管理器】更新编辑开关状态失败", e)
            }
        }
    }

    /**
     * 实时应用格式变化
     */
    private fun applyFormatChangesRealTime(bold: Boolean, italic: Boolean) {
        try {
            remoteTextWindowView?.let { textWindowView ->
                val hasSelection = textWindowView.selectionStart != textWindowView.selectionEnd
                if (hasSelection) {
                    // 应用格式到选中文字
                    textWindowView.applyBoldToSelection(bold)
                    textWindowView.applyItalicToSelection(italic)
                } else {
                    // 应用全局格式
                    textWindowView.setBoldEnabled(bold)
                    textWindowView.setItalicEnabled(italic)
                }
            }
            AppLog.d("【遥控端文字窗口管理器】格式变化已实时应用: 加粗=$bold, 倾斜=$italic")
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】应用格式变化失败", e)
        }
    }

    /**
     * 实时应用字号变化
     */
    private fun applyFontSizeChangeRealTime(fontSize: Int) {
        try {
            remoteTextWindowView?.let { textWindowView ->
                val hasSelection = textWindowView.selectionStart != textWindowView.selectionEnd
                if (hasSelection) {
                    textWindowView.applyFontSizeToSelection(fontSize)
                } else {
                    textWindowView.setFontSize(fontSize)
                }
            }
            AppLog.d("【遥控端文字窗口管理器】字号变化已实时应用: ${fontSize}sp")
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】应用字号变化失败", e)
        }
    }



    /**
     * 实时应用字体变化
     */
    private fun applyFontFamilyChangeRealTime(fontItem: com.example.castapp.utils.FontPresetManager.FontItem) {
        try {
            remoteTextWindowView?.let { textWindowView ->
                val hasSelection = textWindowView.selectionStart != textWindowView.selectionEnd
                if (hasSelection) {
                    textWindowView.applyFontFamilyToSelection(fontItem)
                } else {
                    textWindowView.setFontFamily(fontItem)
                }
            }
            AppLog.d("【遥控端文字窗口管理器】字体变化已实时应用: ${fontItem.name}")
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】应用字体变化失败", e)
        }
    }

    /**
     * 实时应用字间距变化
     */
    private fun applyLetterSpacingChangeRealTime(letterSpacing: Float) {
        try {
            remoteTextWindowView?.let { textWindowView ->
                val hasSelection = textWindowView.selectionStart != textWindowView.selectionEnd
                if (hasSelection) {
                    textWindowView.applyLetterSpacingToSelection(letterSpacing)
                } else {
                    textWindowView.letterSpacing = letterSpacing
                }
            }
            AppLog.d("【遥控端文字窗口管理器】字间距变化已实时应用: ${letterSpacing}em")
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】应用字间距变化失败", e)
        }
    }

    /**
     * 实时应用行间距变化
     */
    private fun applyLineSpacingChangeRealTime(lineSpacing: Float) {
        try {
            remoteTextWindowView?.let { textWindowView ->
                textWindowView.applyLineSpacingToSelection(lineSpacing)
            }

            // 🎯 关键修复：保存行间距到SharedPreferences
            saveLineSpacingToPreferences(lineSpacing)

            AppLog.d("【遥控端文字窗口管理器】行间距变化已实时应用: ${lineSpacing}dp")
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】应用行间距变化失败", e)
        }
    }

    /**
     * 实时应用文本对齐变化
     */
    private fun applyTextAlignmentChangeRealTime(alignment: Int) {
        try {
            remoteTextWindowView?.let { textWindowView ->
                textWindowView.gravity = alignment
            }
            AppLog.d("【遥控端文字窗口管理器】文本对齐变化已实时应用: $alignment")
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】应用文本对齐变化失败", e)
        }
    }

    /**
     * 🎯 关键修复：实时应用字色变化
     */
    private fun applyColorChangeRealTime(color: Int) {
        try {
            remoteTextWindowView?.let { textWindowView ->
                val hasSelection = textWindowView.selectionStart != textWindowView.selectionEnd
                if (hasSelection) {
                    // 应用颜色到选中文字
                    textWindowView.applyColorToSelection(color)
                } else {
                    // 暂时不支持全局颜色设置，提示用户选择文字
                    AppLog.d("【遥控端文字窗口管理器】无选中文字，暂不支持全局颜色设置")
                }
            }
            AppLog.d("【遥控端文字窗口管理器】字色变化已实时应用: ${String.format("#%08X", color)}")
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】应用字色变化失败", e)
        }
    }

    /**
     * 🎯 关键修复：实时应用描边变化
     */
    private fun applyStrokeChangeRealTime(enabled: Boolean, strokeWidth: Float, strokeColor: Int) {
        try {
            remoteTextWindowView?.let { textWindowView ->
                val hasSelection = textWindowView.selectionStart != textWindowView.selectionEnd
                if (hasSelection) {
                    // 应用描边到选中文字
                    textWindowView.applyStrokeToSelection(enabled, strokeWidth, strokeColor)
                } else {
                    // 暂时不支持全局描边设置，提示用户选择文字
                    AppLog.d("【遥控端文字窗口管理器】无选中文字，暂不支持全局描边设置")
                }
            }
            AppLog.d("【遥控端文字窗口管理器】描边变化已实时应用: 启用=$enabled, 宽度=${strokeWidth}px, 颜色=${String.format("#%08X", strokeColor)}")
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】应用描边变化失败", e)
        }
    }

    /**
     * 🎯 架构重构：实时应用窗色变化（统一配置为数据源）
     */
    private fun applyWindowColorChangeRealTime(enabled: Boolean, color: Int) {
        try {
            // 🎯 架构重构：第一步 - 更新统一配置管理器（唯一数据源）
            configManager.updateWindowConfig(remoteReceiverConnection.id, textId) { currentConfig ->
                currentConfig.copy(
                    isWindowTextColorEnabled = enabled,
                    windowTextBackgroundColor = color,
                    lastUpdated = System.currentTimeMillis()
                )
            }
            AppLog.d("【遥控端文字窗口管理器】🎯 统一配置已更新: 启用=$enabled, 颜色=${String.format("#%08X", color)}")

            // 🎯 架构重构：第二步 - 从统一配置同步到所有UI组件
            remoteTextWindowView?.let { textWindowView ->
                textWindowView.setWindowBackgroundColor(enabled, color)
                AppLog.d("【遥控端文字窗口管理器】🎯 TextWindowView已同步")
            }

            targetWindowContainerView?.let { containerView ->
                if (enabled) {
                    containerView.setBackgroundColor(color)
                    AppLog.d("【遥控端文字窗口管理器】🎯 容器视图已同步: ${String.format("#%08X", color)}")
                } else {
                    containerView.setBackgroundColor(android.graphics.Color.TRANSPARENT)
                    AppLog.d("【遥控端文字窗口管理器】🎯 容器视图已设置为透明")
                }
            }

            AppLog.d("【遥控端文字窗口管理器】🎯 窗色变化已通过统一配置同步完成")
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】应用窗色变化失败", e)
        }
    }

    /**
     * 强制同步格式数据（不检查同步开关状态）
     */
    private fun forceSyncFormatData() {
        try {
            AppLog.d("【遥控端文字窗口管理器】强制同步格式数据: $textId")

            // 获取当前格式数据
            val formatData = collectCurrentFormatData()

            // 发送同步消息到接收端
            sendFormatSyncMessage(formatData)

            // 🎯 关键修复：更新统一配置管理器中的文字内容，使用formatData中的实际内容
            val actualTextContent = formatData["textContent"] as? String ?: currentTextContent
            updateUnifiedConfigTextContent(actualTextContent, formatData)
            AppLog.d("【遥控端文字窗口管理器】已同步更新统一配置管理器中的文字内容: '$actualTextContent'")

        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】强制同步格式数据失败", e)
        }
    }

    /**
     * 检查并同步格式数据（根据同步开关状态）
     */
    private fun checkAndSyncFormatData() {
        try {
            AppLog.d("【遥控端文字窗口管理器】检查格式数据同步: $textId")

            // 检查同步开关状态
            if (!isSyncEnabled()) {
                AppLog.d("【遥控端文字窗口管理器】同步开关未开启，跳过格式同步")
                return
            }

            // 强制同步
            forceSyncFormatData()

        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】同步格式数据失败", e)
        }
    }

    /**
     * 🎯 从TextWindowView同步格式状态到管理器
     */
    private fun syncFormatStateFromTextView() {
        try {
            remoteTextWindowView?.let { textView ->
                if (textView.getIsInEditMode()) {
                    // 获取选中文字的完整格式状态
                    val formatState = textView.getSelectionFormatStateWithFontSize()
                    val newBold = formatState.first
                    val newItalic = formatState.second
                    val newFontSize = formatState.third

                    // 更新管理器的格式状态
                    if (isBoldEnabled != newBold || isItalicEnabled != newItalic || currentFontSize != newFontSize) {
                        isBoldEnabled = newBold
                        isItalicEnabled = newItalic
                        currentFontSize = newFontSize
                        AppLog.d("【遥控端文字窗口管理器】基本格式状态已从TextWindowView同步: 加粗=$isBoldEnabled, 倾斜=$isItalicEnabled, 字号=${currentFontSize}sp")
                    }

                    // 🎯 关键修复：同步TextView级别的属性（行间距和对齐方式）
                    // 这些属性不能从选中文字检测，需要从TextView本身或SharedPreferences获取
                    val currentLineSpacingValue = getCurrentLineSpacing()
                    val currentAlignment = textView.getTextGravity()

                    AppLog.d("【遥控端文字窗口管理器】TextView级别属性已同步: 行间距=${currentLineSpacingValue}dp, 对齐=$currentAlignment")
                }
            }
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】同步格式状态失败", e)
        }
    }

    /**
     * 获取当前行间距
     */
    private fun getCurrentLineSpacing(): Float {
        return try {
            // 🎯 关键修复：使用与TextWindowManager一致的SharedPreferences键名
            val sharedPreferences = context.getSharedPreferences("text_format_preferences", android.content.Context.MODE_PRIVATE)
            val lineSpacing = sharedPreferences.getFloat("line_spacing_$textId", 0.0f)
            currentLineSpacing = lineSpacing
            lineSpacing
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】获取行间距失败", e)
            0.0f
        }
    }

    /**
     * 获取当前字体
     */
    private fun getCurrentFontFamily(): com.example.castapp.utils.FontPresetManager.FontItem? {
        return try {
            // 从SharedPreferences获取字体设置
            val sharedPreferences = context.getSharedPreferences("text_window_settings", android.content.Context.MODE_PRIVATE)
            val fontName = sharedPreferences.getString("font_family_$textId", null)
            if (fontName != null) {
                // 确保FontPresetManager已初始化
                com.example.castapp.utils.FontPresetManager.initialize(context)
                val fontItem = com.example.castapp.utils.FontPresetManager.getFontByName(fontName)
                currentFontFamily = fontItem
                fontItem
            } else {
                null
            }
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】获取字体失败", e)
            null
        }
    }

    /**
     * 获取当前字间距
     */
    private fun getCurrentLetterSpacing(): Float {
        return try {
            // 从SharedPreferences获取字间距设置
            val sharedPreferences = context.getSharedPreferences("text_window_settings", android.content.Context.MODE_PRIVATE)
            val letterSpacing = sharedPreferences.getFloat("letter_spacing_$textId", 0.0f)
            currentLetterSpacing = letterSpacing
            letterSpacing
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】获取字间距失败", e)
            0.0f
        }
    }

    /**
     * 保存行距到SharedPreferences
     */
    private fun saveLineSpacingToPreferences(lineSpacing: Float) {
        try {
            // 🎯 关键修复：使用与TextWindowManager一致的SharedPreferences键名
            val sharedPreferences = context.getSharedPreferences("text_format_preferences", android.content.Context.MODE_PRIVATE)
            sharedPreferences.edit()
                .putFloat("line_spacing_$textId", lineSpacing)
                .apply()
            AppLog.d("【遥控端文字窗口管理器】行距已保存到SharedPreferences: ${lineSpacing}dp")
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】保存行距失败", e)
        }
    }

    /**
     * 🎯 关键修复：执行窗口容器拖动（参考接收端实现）
     */
    private fun performWindowContainerDrag(deltaX: Float, deltaY: Float) {
        try {
            targetWindowContainerView?.let { containerView ->
                // 🎯 关键修复：参考接收端实现，使用变换方式而不是直接修改位置
                // 计算新的变换位置
                val currentTranslationX = containerView.translationX
                val currentTranslationY = containerView.translationY

                val newTranslationX = currentTranslationX + deltaX
                val newTranslationY = currentTranslationY + deltaY

                // 应用变换
                containerView.translationX = newTranslationX
                containerView.translationY = newTranslationY

                // 🎯 关键修复：同步更新文字窗口边框的变换
                updateWindowBorderTranslation(containerView, newTranslationX, newTranslationY)

                AppLog.v("【遥控端文字窗口管理器】窗口容器拖动: deltaX=$deltaX, deltaY=$deltaY, 新变换: ($newTranslationX, $newTranslationY)")
            }
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】执行窗口容器拖动失败", e)
        }
    }

    /**
     * 🎯 关键修复：通知窗口容器拖动结束
     */
    private fun notifyWindowContainerDragEnd() {
        try {
            targetWindowContainerView?.let { containerView ->
                val finalX = containerView.x
                val finalY = containerView.y

                AppLog.d("【遥控端文字窗口管理器】窗口容器拖动结束，最终位置: ($finalX, $finalY)")

                // TODO: 如果需要同步拖动结果到接收端，可以在这里实现
                // 例如：发送窗口位置更新消息到接收端
            }
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】通知窗口容器拖动结束失败", e)
        }
    }

    /**
     * 🎯 关键修复：更新文字窗口边框变换（参考接收端实现）
     */
    private fun updateWindowBorderTranslation(containerView: com.example.castapp.ui.view.WindowVisualizationContainerView, translationX: Float, translationY: Float) {
        try {
            // 通过反射获取边框View引用
            val borderViewRefField = containerView::class.java.getDeclaredField("borderViewRef")
            borderViewRefField.isAccessible = true
            val borderViewRef = borderViewRefField.get(containerView) as? java.lang.ref.WeakReference<*>

            val borderView = borderViewRef?.get() as? android.view.View
            if (borderView != null) {
                // 🎯 关键修复：参考接收端实现，使用变换而不是直接位置
                // 边框的变换应该与容器的变换保持一致
                borderView.translationX = translationX
                borderView.translationY = translationY

                AppLog.v("【遥控端文字窗口管理器】文字窗口边框变换已同步: translation($translationX, $translationY)")
            } else {
                AppLog.v("【遥控端文字窗口管理器】未找到文字窗口边框视图")
            }
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】更新文字窗口边框变换失败", e)
        }
    }

    /**
     * 🎯 关键修复：清除选中文字的格式
     */
    private fun clearSelectionFormat() {
        try {
            remoteTextWindowView?.let { textWindowView ->
                // 调用TextWindowView的清除格式方法
                textWindowView.clearSelectionFormat()

                // 重置管理器的格式状态
                isBoldEnabled = false
                isItalicEnabled = false
                currentFontSize = 13 // 重置为默认字号
                currentFontFamily = null // 重置为默认字体
                currentLetterSpacing = 0.0f // 重置为默认字间距

                // 🎯 关键修复：重置对齐方式为默认居中对齐
                val defaultAlignment = android.view.Gravity.CENTER_HORIZONTAL or android.view.Gravity.CENTER_VERTICAL
                textWindowView.setTextGravity(defaultAlignment)

                AppLog.d("【遥控端文字窗口管理器】选中文字格式已清除，对齐方式已重置为居中")

                // 🎯 关键修复：同步格式数据到接收端
                checkAndSyncFormatData()
            }
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】清除选中文字格式失败", e)
        }
    }

    /**
     * 检查同步开关状态
     */
    private fun isSyncEnabled(): Boolean {
        return try {
            // 🎯 优先使用同步回调函数
            if (syncCallback != null) {
                val isEnabled = syncCallback!!.invoke()
                AppLog.d("【遥控端文字窗口管理器】同步开关状态（通过回调）: $isEnabled")
                return isEnabled
            }

            // 后备方案：通过RemoteReceiverManager检查同步状态
            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            val isEnabled = manager.isSyncEnabledForReceiver(remoteReceiverConnection.id)
            AppLog.d("【遥控端文字窗口管理器】同步开关状态（通过管理器）: $isEnabled")
            isEnabled
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】检查同步开关状态失败", e)
            false
        }
    }

    /**
     * 🎯 启用边框拖动调整大小功能
     */
    private fun enableBorderResizing() {
        try {
            // 🎯 关键修复：为遥控端TextWindowView启用边框拖动调整大小功能
            remoteTextWindowView?.let { textWindowView ->
                // 设置边框拖动调整大小监听器
                textWindowView.setOnBorderResizeListener { newWidth, newHeight ->
                    // 当用户通过拖动边框调整大小时，同步更新容器尺寸
                    updateRemoteTextWindowSize(newWidth, newHeight)
                }
                AppLog.d("【遥控端文字窗口管理器】边框拖动调整大小功能已启用")
            }
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】启用边框拖动功能失败", e)
        }
    }

    /**
     * 🎯 禁用边框拖动调整大小功能
     */
    private fun disableBorderResizing() {
        try {
            // 🎯 关键修复：为遥控端TextWindowView禁用边框拖动调整大小功能
            remoteTextWindowView?.let { textWindowView ->
                // 移除边框拖动调整大小监听器
                textWindowView.setOnBorderResizeListener(null)
                AppLog.d("【遥控端文字窗口管理器】边框拖动调整大小功能已禁用")
            }
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】禁用边框拖动功能失败", e)
        }
    }

    /**
     * 🎯 更新遥控端文字窗口尺寸
     */
    private fun updateRemoteTextWindowSize(newWidth: Int, newHeight: Int) {
        try {
            AppLog.d("【遥控端文字窗口管理器】开始更新文字窗口尺寸: ${newWidth}x${newHeight}")

            // 更新TextWindowView的尺寸
            remoteTextWindowView?.setWindowSize(newWidth, newHeight)

            // 更新容器视图的尺寸
            targetWindowContainerView?.let { containerView ->
                AppLog.d("【遥控端文字窗口管理器】更新容器视图尺寸，当前尺寸: ${containerView.width}x${containerView.height}")

                val layoutParams = containerView.layoutParams
                if (layoutParams != null) {
                    layoutParams.width = newWidth
                    layoutParams.height = newHeight
                    containerView.layoutParams = layoutParams

                    // 🎯 关键修复：强制重新布局以确保尺寸变化生效
                    containerView.requestLayout()

                    // 🎯 额外修复：强制重绘以确保视觉更新
                    containerView.invalidate()

                    // 🎯 新增修复：更新边框View尺寸以同步容器尺寸变化
                    updateContainerBorderView(containerView)

                    AppLog.d("【遥控端文字窗口管理器】容器视图布局参数已更新并强制重新布局")
                } else {
                    AppLog.w("【遥控端文字窗口管理器】容器视图的layoutParams为null，无法更新尺寸")
                }
            } ?: run {
                AppLog.w("【遥控端文字窗口管理器】targetWindowContainerView为null，无法更新容器尺寸")
            }

            AppLog.d("【遥控端文字窗口管理器】文字窗口尺寸更新完成: ${newWidth}x${newHeight}")

        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】更新文字窗口尺寸失败", e)
        }
    }

    /**
     * 🎯 新增：更新容器边框View尺寸（平滑更新，避免闪烁）
     */
    private fun updateContainerBorderView(containerView: com.example.castapp.ui.view.WindowVisualizationContainerView) {
        try {
            // 通过反射获取容器的窗口数据
            val windowDataField = containerView::class.java.getDeclaredField("windowData")
            windowDataField.isAccessible = true
            val windowData = windowDataField.get(containerView) as? com.example.castapp.model.WindowVisualizationData

            if (windowData != null && windowData.isBorderEnabled) {
                AppLog.d("【遥控端文字窗口管理器】检测到边框启用，开始平滑更新边框View尺寸")

                // 🎯 关键修复：直接更新现有边框View的尺寸，而不是重新创建
                updateExistingBorderViewSize(containerView, windowData)

                AppLog.d("【遥控端文字窗口管理器】边框View尺寸已平滑更新")
            } else {
                AppLog.d("【遥控端文字窗口管理器】容器无边框或边框未启用，跳过边框更新")
            }

        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】更新容器边框View失败", e)
        }
    }

    /**
     * 🎯 新增：平滑更新现有边框View的尺寸（避免重新创建导致的闪烁）
     */
    private fun updateExistingBorderViewSize(containerView: com.example.castapp.ui.view.WindowVisualizationContainerView, windowData: com.example.castapp.model.WindowVisualizationData) {
        try {
            // 通过反射获取边框View引用
            val borderViewRefField = containerView::class.java.getDeclaredField("borderViewRef")
            borderViewRefField.isAccessible = true
            val borderViewRef = borderViewRefField.get(containerView) as? java.lang.ref.WeakReference<*>

            val borderView = borderViewRef?.get() as? android.view.View
            if (borderView != null) {
                AppLog.d("【遥控端文字窗口管理器】找到现有边框View，开始更新尺寸")

                // 计算新的边框尺寸和位置
                val borderWidthPx = dpToPx(windowData.borderWidth).toInt()
                val clipBounds = containerView.clipBounds

                val (borderLeft, borderTop, borderWidth, borderHeight) = if (clipBounds != null) {
                    // 裁剪状态：基于clipBounds计算边框位置
                    val left = clipBounds.left - borderWidthPx
                    val top = clipBounds.top - borderWidthPx
                    val width = clipBounds.width() + borderWidthPx * 2
                    val height = clipBounds.height() + borderWidthPx * 2
                    arrayOf(left, top, width, height)
                } else {
                    // 未裁剪状态：基于整个容器计算边框位置
                    val left = -borderWidthPx
                    val top = -borderWidthPx
                    val width = containerView.width + borderWidthPx * 2
                    val height = containerView.height + borderWidthPx * 2
                    arrayOf(left, top, width, height)
                }

                // 🎯 关键修复：直接更新边框View的布局参数，避免重新创建
                val layoutParams = borderView.layoutParams as? android.widget.FrameLayout.LayoutParams
                if (layoutParams != null) {
                    layoutParams.width = borderWidth
                    layoutParams.height = borderHeight
                    layoutParams.leftMargin = borderLeft
                    layoutParams.topMargin = borderTop
                    borderView.layoutParams = layoutParams

                    // 🎯 强制重新布局和重绘
                    borderView.requestLayout()
                    borderView.invalidate()

                    AppLog.d("【遥控端文字窗口管理器】边框View布局参数已更新: 尺寸=${borderWidth}x${borderHeight}, 位置=(${borderLeft}, ${borderTop})")
                } else {
                    AppLog.w("【遥控端文字窗口管理器】边框View的layoutParams不是FrameLayout.LayoutParams类型")
                }

            } else {
                AppLog.w("【遥控端文字窗口管理器】未找到现有边框View，可能需要重新创建")
                // 如果边框View不存在，回退到重新创建的方式
                val updateBorderViewMethod = containerView::class.java.getDeclaredMethod("updateBorderView", com.example.castapp.model.WindowVisualizationData::class.java)
                updateBorderViewMethod.isAccessible = true
                updateBorderViewMethod.invoke(containerView, windowData)
            }

        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】平滑更新边框View尺寸失败", e)
        }
    }

    /**
     * 🎯 工具方法：dp转px
     */
    private fun dpToPx(dp: Float): Float {
        return dp * context.resources.displayMetrics.density
    }

    /**
     * 🎯 架构重构：收集当前格式数据（从统一配置获取）
     */
    private fun collectCurrentFormatData(): Map<String, Any> {
        val formatData = mutableMapOf<String, Any>()

        try {
            // 🎯 架构重构：优先从统一配置获取数据
            val config = getCurrentConfig()
            if (config != null) {
                // 从统一配置获取基本格式信息
                formatData["textContent"] = config.textContent
                formatData["isBold"] = config.isBold
                formatData["isItalic"] = config.isItalic
                formatData["fontSize"] = config.fontSize
                formatData["connectionId"] = textId
                formatData["textAlignment"] = config.textAlignment
                formatData["lineSpacing"] = config.lineSpacing
                formatData["isWindowColorEnabled"] = config.isWindowTextColorEnabled
                formatData["windowBackgroundColor"] = config.windowTextBackgroundColor

                AppLog.d("【遥控端文字窗口管理器】🎯 已从统一配置收集基本格式数据")
            }

            // 🎯 从TextWindowView获取富文本格式（这部分仍需要从UI获取）
            remoteTextWindowView?.let { textWindowView ->
                val spannableText = textWindowView.text
                if (spannableText != null && spannableText.isNotEmpty()) {
                    try {
                        val spannableString = android.text.SpannableString(spannableText)
                        val richTextData = textFormatManager.serializeSpannableString(spannableString)
                        formatData["richTextData"] = richTextData

                        // 更新统一配置中的富文本数据
                        configManager.updateWindowConfig(remoteReceiverConnection.id, textId) { currentConfig ->
                            currentConfig.copy(
                                richTextData = richTextData,
                                textContent = spannableText.toString(),
                                lastUpdated = System.currentTimeMillis()
                            )
                        }

                        AppLog.d("【遥控端文字窗口管理器】🎯 富文本数据已同步到统一配置")
                    } catch (e: Exception) {
                        AppLog.w("【遥控端文字窗口管理器】序列化富文本失败", e)
                    }
                }
            }

                // 扩展格式信息
                try {
                    // 获取文本对齐
                    formatData["textAlignment"] = textWindowView.getTextGravity()

                    // 获取字间距
                    formatData["letterSpacing"] = textWindowView.letterSpacing

                    // 🎯 关键修复：获取实际的字体信息，而不是硬编码
                    val currentFont = textWindowView.getCurrentFontFamily()
                    if (currentFont != null) {
                        formatData["fontName"] = currentFont.name
                        formatData["fontFamily"] = currentFont.fontFamily
                        AppLog.d("【遥控端文字窗口管理器】获取到实际字体信息: ${currentFont.name}")
                    } else {
                        // 后备方案：使用默认字体信息
                        formatData["fontName"] = "Roboto"
                        formatData["fontFamily"] = "sans-serif"
                        AppLog.w("【遥控端文字窗口管理器】未获取到字体信息，使用默认字体")
                    }

                    // 🎯 关键修复：获取实际的行间距值，而不是硬编码
                    formatData["lineSpacing"] = getCurrentLineSpacing()

                } catch (e: Exception) {
                    AppLog.w("【遥控端文字窗口管理器】获取扩展格式信息失败", e)
                }

                // 🎯 关键修复：收集窗口背景颜色信息
                try {
                    val windowColorState = textWindowView.getWindowBackgroundColorState()
                    formatData["isWindowColorEnabled"] = windowColorState.first
                    formatData["windowBackgroundColor"] = windowColorState.second
                    AppLog.d("【遥控端文字窗口管理器】窗口背景颜色信息已收集: 启用=${windowColorState.first}, 颜色=${String.format("#%08X", windowColorState.second)}")
                } catch (e: Exception) {
                    AppLog.w("【遥控端文字窗口管理器】获取窗口背景颜色信息失败", e)
                }
            }

            // 🎯 新增：收集窗口变换信息
            try {
                val transformData = collectWindowTransformData()
                if (transformData.isNotEmpty()) {
                    formatData["windowTransform"] = transformData
                    AppLog.d("【遥控端文字窗口管理器】窗口变换数据已收集: ${transformData.keys}")
                }
            } catch (e: Exception) {
                AppLog.w("【遥控端文字窗口管理器】收集窗口变换数据失败", e)
            }

            AppLog.d("【遥控端文字窗口管理器】格式数据收集完成: ${formatData.keys}")

        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】收集格式数据失败", e)
        }

        return formatData
    }

    /**
     * 🎯 收集窗口变换数据
     */
    private fun collectWindowTransformData(): Map<String, Any> {
        val transformData = mutableMapOf<String, Any>()

        try {
            // 从WindowContainerVisualizationView中查找对应的窗口容器
            windowVisualizationView?.let { visualizationView ->
                // 查找对应的WindowVisualizationContainerView
                for (i in 0 until visualizationView.childCount) {
                    val child = visualizationView.getChildAt(i)
                    if (child is com.example.castapp.ui.view.WindowVisualizationContainerView) {
                        // 检查是否是对应的文字窗口
                        val windowData = child.getWindowData()
                        if (windowData?.connectionId == textId) {
                            // 🎯 关键修复：收集当前实际位置，而不是原始位置
                            // 用户在编辑模式下可能拖动了窗口，需要同步当前位置到接收端
                            val currentX = child.x
                            val currentY = child.y

                            // 将遥控端坐标转换为接收端坐标
                            val (actualX, actualY) = com.example.castapp.utils.WindowScaleCalculator.convertRemoteToActualCoordinates(
                                remoteX = currentX,
                                remoteY = currentY,
                                remoteControlScale = windowData.remoteControlScale
                            )

                            transformData["x"] = actualX
                            transformData["y"] = actualY

                            AppLog.d("【遥控端文字窗口管理器】位置数据收集:")
                            AppLog.d("  遥控端当前位置: ($currentX, $currentY)")
                            AppLog.d("  远程控制缩放因子: ${windowData.remoteControlScale}")
                            AppLog.d("  🎯 转换后的接收端位置: ($actualX, $actualY)")

                            // 🎯 关键修复：收集用户调整后的可视化容器尺寸
                            // 接收端会除以remoteControlScale来计算实际窗口尺寸
                            transformData["width"] = child.width
                            transformData["height"] = child.height

                            // 🎯 添加远程控制缩放因子，供接收端计算使用
                            transformData["remoteControlScale"] = windowData.remoteControlScale

                            // 收集变换信息
                            transformData["scaleX"] = child.scaleX
                            transformData["scaleY"] = child.scaleY
                            transformData["rotation"] = child.rotation

                            // 收集透明度
                            transformData["alpha"] = child.alpha

                            AppLog.d("【遥控端文字窗口管理器】窗口变换数据收集完成:")
                            AppLog.d("  原始位置: (${windowData.originalX}, ${windowData.originalY})")
                            AppLog.d("  原始尺寸: ${windowData.originalWidth}x${windowData.originalHeight}")
                            AppLog.d("  可视化位置: (${child.x}, ${child.y})")
                            AppLog.d("  🎯 用户调整后的可视化尺寸: ${child.width}x${child.height}")
                            AppLog.d("  远程控制缩放因子: ${windowData.remoteControlScale}")
                            AppLog.d("  缩放: (${child.scaleX}, ${child.scaleY})")
                            AppLog.d("  旋转: ${child.rotation}°")
                            AppLog.d("  透明度: ${child.alpha}")
                            AppLog.d("  🎯 发送可视化尺寸，接收端将除以缩放因子计算实际尺寸")

                            break
                        }
                    }
                }
            }
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】收集窗口变换数据失败", e)
        }

        return transformData
    }

    /**
     * 发送格式同步消息
     */
    private fun sendFormatSyncMessage(formatData: Map<String, Any>) {
        try {
            AppLog.d("【遥控端文字窗口管理器】发送格式同步消息: $textId")

            // 通过RemoteReceiverManager发送格式同步消息
            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            manager.sendTextFormatSyncMessage(remoteReceiverConnection, formatData)

            AppLog.d("【遥控端文字窗口管理器】格式同步消息已发送")

        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】发送格式同步消息失败", e)
        }
    }

    /**
     * 🎯 关键修复：更新统一配置管理器中的文字内容
     */
    private fun updateUnifiedConfigTextContent(newTextContent: String, formatData: Map<String, Any>) {
        try {
            // 获取统一配置管理器
            val configManager = com.example.castapp.model.RemoteWindowConfigManager.getInstance()

            // 更新文字内容
            configManager.updateTextContent(
                receiverId = remoteReceiverConnection.id,
                connectionId = textId,
                textContent = newTextContent,
                richTextData = formatData["richTextData"] as? String
            )

            // 更新文字格式
            configManager.updateTextFormat(
                receiverId = remoteReceiverConnection.id,
                connectionId = textId,
                isBold = formatData["isBold"] as? Boolean,
                isItalic = formatData["isItalic"] as? Boolean,
                fontSize = formatData["fontSize"] as? Int,
                fontName = formatData["fontName"] as? String,
                fontFamily = formatData["fontFamily"] as? String,
                lineSpacing = formatData["lineSpacing"] as? Float,
                textAlignment = formatData["textAlignment"] as? Int
            )

            AppLog.d("【遥控端文字窗口管理器】统一配置管理器文字内容更新完成: $textId")
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】更新统一配置管理器文字内容失败", e)
        }
    }

    /**
     * 获取主容器（用于显示编辑面板）
     */
    private fun getMainContainer(): FrameLayout? {
        return try {
            // 优先使用WindowContainerVisualizationView作为容器
            if (windowVisualizationView != null) {
                AppLog.d("【遥控端文字窗口管理器】使用窗口可视化容器")
                return windowVisualizationView as? FrameLayout
            }

            // 尝试从缓存获取
            mainContainerRef?.get() ?: run {
                // 通过MainActivity获取主容器（作为备选方案）
                val activity = context as? androidx.fragment.app.FragmentActivity
                val mainContainer = activity?.findViewById<FrameLayout>(android.R.id.content)
                if (mainContainer != null) {
                    mainContainerRef = WeakReference(mainContainer)
                    AppLog.w("【遥控端文字窗口管理器】使用主界面容器作为备选方案")
                }
                mainContainer
            }
        } catch (e: Exception) {
            AppLog.e("【遥控端文字窗口管理器】获取主容器失败", e)
            null
        }
    }
}
